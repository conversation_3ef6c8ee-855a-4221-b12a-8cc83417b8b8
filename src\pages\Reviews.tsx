
import React, { useState, useEffect } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Star, Shield, Loader2, Search, ThumbsUp, MessageCircle } from 'lucide-react';
import { FirebaseService } from '@/services/firebase';
import { Review } from '@/types/firebase';
import { useToast } from '@/hooks/use-toast';

const Reviews = () => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');
  const [sortBy, setSortBy] = useState('newest');
  const [searchTerm, setSearchTerm] = useState('');
  const { toast } = useToast();

  const [stats, setStats] = useState({
    totalReviews: 0,
    averageRating: 0,
    ratingBreakdown: {
      5: 0,
      4: 0,
      3: 0,
      2: 0,
      1: 0
    }
  });

  useEffect(() => {
    const loadReviews = async () => {
      try {
        setLoading(true);
        const reviewsData = await FirebaseService.getAllReviews();
        const typedReviews = reviewsData as Review[];
        setReviews(typedReviews);
        
        // Calculate stats
        const totalReviews = typedReviews.length;
        const totalRating = typedReviews.reduce((sum, review) => sum + review.rating, 0);
        const averageRating = totalReviews > 0 ? totalRating / totalReviews : 0;
        
        const ratingBreakdown = {
          5: typedReviews.filter(r => r.rating === 5).length,
          4: typedReviews.filter(r => r.rating === 4).length,
          3: typedReviews.filter(r => r.rating === 3).length,
          2: typedReviews.filter(r => r.rating === 2).length,
          1: typedReviews.filter(r => r.rating === 1).length,
        };
        
        setStats({
          totalReviews,
          averageRating: Math.round(averageRating * 10) / 10,
          ratingBreakdown
        });
      } catch (error) {
        console.error('Error loading reviews:', error);
        toast({
          title: "Error loading reviews",
          description: "There was an error loading the reviews.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    loadReviews();
  }, [toast]);

  const filteredAndSortedReviews = React.useMemo(() => {
    let filtered = reviews;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(review => 
        review.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        review.content?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        review.userName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        review.tourName?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply rating filter
    if (filter !== 'all') {
      const rating = parseInt(filter);
      filtered = filtered.filter(review => review.rating === rating);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return (b.createdAt?.toMillis?.() || 0) - (a.createdAt?.toMillis?.() || 0);
        case 'oldest':
          return (a.createdAt?.toMillis?.() || 0) - (b.createdAt?.toMillis?.() || 0);
        case 'highest':
          return b.rating - a.rating;
        case 'lowest':
          return a.rating - b.rating;
        case 'helpful':
          return (b.helpful || 0) - (a.helpful || 0);
        default:
          return 0;
      }
    });

    return filtered;
  }, [reviews, filter, sortBy, searchTerm]);

  const RatingStars = ({ rating, size = 'w-4 h-4' }: { rating: number; size?: string }) => (
    <div className="flex">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={`${size} ${star <= rating ? 'fill-safari-golden-sun text-safari-golden-sun' : 'text-muted'}`}
        />
      ))}
    </div>
  );

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'Unknown date';
    try {
      return timestamp.toDate().toLocaleDateString();
    } catch {
      return 'Unknown date';
    }
  };

  const handleHelpfulClick = async (reviewId: string) => {
    try {
      // In a real implementation, this would update the helpful count in Firebase
      toast({
        title: "Thank you!",
        description: "Your feedback has been recorded."
      });
    } catch (error) {
      console.error('Error updating helpful count:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen">
        <Header />
        <main className="pt-20 flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Loading reviews...</p>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-20">
        {/* Hero Section */}
        <div className="relative h-96 overflow-hidden bg-cover bg-center bg-no-repeat" 
         style={{
              backgroundImage: 'url(https://qconvxhtavuzjdwuowpt.supabase.co/storage/v1/object/public/photoz/image%20(5).png)',    
            }}
        >
          <div 
            className="absolute inset-0 w-full h-full"
           
          />
          <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-black/70" />
          <div className="relative z-10 flex items-center justify-center h-full text-white px-4">
            <div className="text-center max-w-4xl mx-auto">
              <Badge className="mb-4 bg-safari-sunset text-primary-foreground px-4 py-2">
                <Star className="w-4 h-4 mr-2" />
                Customer Reviews
              </Badge>
              <h1 className="text-5xl md:text-6xl font-bubblegum font-bold mb-6">What Our Guests Say</h1>
              <p className="text-xl md:text-2xl leading-relaxed opacity-90">
                Real experiences from satisfied safari adventurers
              </p>
            </div>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="bg-background py-16">
          <div className="container mx-auto px-4">
            <div className="grid md:grid-cols-3 gap-8 text-center">
              <div>
                <div className="text-4xl font-bold text-safari-sunset mb-2">{stats.totalReviews.toLocaleString()}</div>
                <div className="text-muted-foreground">Total Reviews</div>
              </div>
              <div>
                <div className="flex items-center justify-center mb-2">
                  <span className="text-4xl font-bold text-safari-sunset mr-2">{stats.averageRating}</span>
                  <RatingStars rating={Math.round(stats.averageRating)} size="w-6 h-6" />
                </div>
                <div className="text-muted-foreground">Average Rating</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-safari-sunset mb-2">
                  {stats.totalReviews > 0 ? Math.round((stats.ratingBreakdown[4] + stats.ratingBreakdown[5]) / stats.totalReviews * 100) : 0}%
                </div>
                <div className="text-muted-foreground">Would Recommend</div>
              </div>
            </div>

            {/* Rating Breakdown */}
            <div className="mt-12 max-w-2xl mx-auto">
              <h3 className="text-xl font-semibold mb-6 text-center text-foreground">Rating Breakdown</h3>
              <div className="space-y-2">
                {[5, 4, 3, 2, 1].map(rating => (
                  <div key={rating} className="flex items-center">
                    <span className="w-8 text-sm text-foreground">{rating}</span>
                    <Star className="w-4 h-4 text-safari-golden-sun mr-2" />
                    <div className="flex-1 bg-muted rounded-full h-2 mr-4">
                      <div
                        className="bg-safari-sunset h-2 rounded-full"
                        style={{
                          width: stats.totalReviews > 0
                            ? `${(stats.ratingBreakdown[rating as keyof typeof stats.ratingBreakdown] / stats.totalReviews) * 100}%`
                            : '0%'
                        }}
                      />
                    </div>
                    <span className="w-12 text-sm text-muted-foreground">
                      {stats.ratingBreakdown[rating as keyof typeof stats.ratingBreakdown]}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Reviews */}
        <div className="container mx-auto px-4 py-16">
          {/* Search and Filters */}
          <div className="mb-8 flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search reviews..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={filter} onValueChange={setFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Filter by rating" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Ratings</SelectItem>
                <SelectItem value="5">5 Stars</SelectItem>
                <SelectItem value="4">4 Stars</SelectItem>
                <SelectItem value="3">3 Stars</SelectItem>
                <SelectItem value="2">2 Stars</SelectItem>
                <SelectItem value="1">1 Star</SelectItem>
              </SelectContent>
            </Select>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="newest">Newest First</SelectItem>
                <SelectItem value="oldest">Oldest First</SelectItem>
                <SelectItem value="highest">Highest Rating</SelectItem>
                <SelectItem value="lowest">Lowest Rating</SelectItem>
                <SelectItem value="helpful">Most Helpful</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Reviews List */}
          <div className="space-y-8">
            {filteredAndSortedReviews.length === 0 ? (
              <Card>
                <CardContent className="p-12 text-center">
                  <div className="text-gray-400 mb-4">
                    <Star className="w-12 h-12 mx-auto" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">No reviews found</h3>
                  <p className="text-gray-600">
                    {searchTerm || filter !== 'all' 
                      ? 'Try adjusting your search or filters.'
                      : 'Be the first to leave a review!'}
                  </p>
                </CardContent>
              </Card>
            ) : (
              filteredAndSortedReviews.map((review) => (
                <Card key={review.id} className="overflow-hidden">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-start gap-4">
                        <Avatar className="h-12 w-12">
                          {review.userAvatar ? (
                            <img src={review.userAvatar} alt={review.userName || 'User'} className="object-cover" />
                          ) : (
                            <div className="w-full h-full bg-safari-warm-amber/20 flex items-center justify-center text-safari-sunset font-semibold">
                              {(review.userName || 'U').charAt(0).toUpperCase()}
                            </div>
                          )}
                        </Avatar>
                        <div>
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-semibold">{review.userName || 'Anonymous User'}</h4>
                            {review.verified && (
                              <Badge variant="outline" className="text-safari-coffee-brown border-safari-coffee-brown">
                                <Shield className="w-3 h-3 mr-1" />
                                Verified
                              </Badge>
                            )}
                          </div>
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <RatingStars rating={review.rating} />
                            <span>{formatDate(review.createdAt)}</span>
                            <span>•</span>
                            <span>{review.tourName || 'Safari Tour'}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <h3 className="font-semibold text-lg mb-2">{review.title}</h3>
                    <p className="text-gray-700 mb-4">{review.content}</p>

                    {review.images && review.images.length > 0 && (
                      <div className="flex gap-2 mb-4">
                        {review.images.map((image, index) => (
                          <img
                            key={index}
                            src={image}
                            alt={`Review image ${index + 1}`}
                            className="w-24 h-24 object-cover rounded-lg"
                          />
                        ))}
                      </div>
                    )}

                    <div className="flex items-center gap-4 pt-4 border-t">
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleHelpfulClick(review.id)}
                      >
                        <ThumbsUp className="w-4 h-4 mr-2" />
                        Helpful ({review.helpful || 0})
                      </Button>
                      <Button variant="ghost" size="sm">
                        <MessageCircle className="w-4 h-4 mr-2" />
                        Reply
                      </Button>
                    </div>

                    {review.response && (
                      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                        <div className="font-semibold text-sm mb-1">{review.response.author}</div>
                        <p className="text-sm text-gray-700">{review.response.content}</p>
                        <div className="text-xs text-gray-500 mt-2">{review.response.date}</div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))
            )}
          </div>

          {/* Load More Button - for pagination in future */}
          {filteredAndSortedReviews.length > 0 && (
            <div className="text-center mt-8">
              <Button variant="outline" className="px-8">
                Load More Reviews
              </Button>
            </div>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Reviews;
