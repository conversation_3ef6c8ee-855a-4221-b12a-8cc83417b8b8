
import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MapPin, Camera, Star, Calendar, ArrowRight, Globe, Navigation } from 'lucide-react';
import LeafletMap from '@/components/maps/LeafletMap';

const DestinationShowcase = () => {
  const [selectedDestination, setSelectedDestination] = useState<any>(null);

  const destinations = [
    {
      id: 'serengeti',
      name: 'Serengeti National Park',
      lat: -2.153389,
      lng: 34.6857,
      image: 'photo-1472396961693-142e6e269027',
      description: 'Home to the Great Migration and endless plains teeming with wildlife',
      highlights: ['Great Migration', 'Big Five', 'Endless Plains'],
      bestTime: 'June - October',
      rating: 4.9,
      area: '14,750 km²',
      established: '1951',
      tours: 12
    },
    {
      id: 'ngorongoro',
      name: 'Ngorongoro Crater',
      lat: -3.2175,
      lng: 35.5,
      image: 'photo-1466721591366-2d5fba72006d',
      description: 'A natural wonder featuring the world\'s largest intact volcanic caldera',
      highlights: ['Black Rhinos', 'Crater Floor', 'Flamingo Lake'],
      bestTime: 'Year Round',
      rating: 4.8,
      area: '8,292 km²',
      established: '1959',
      tours: 8
    },
    {
      id: 'tarangire',
      name: 'Tarangire National Park',
      lat: -3.8333,
      lng: 35.85,
      image: 'photo-1493962853295-0fd70327578a',
      description: 'Famous for large elephant herds and iconic baobab trees',
      highlights: ['Elephant Herds', 'Baobab Trees', 'Bird Watching'],
      bestTime: 'June - November',
      rating: 4.7,
      area: '2,850 km²',
      established: '1970',
      tours: 6
    },
    {
      id: 'manyara',
      name: 'Lake Manyara',
      lat: -3.3667,
      lng: 35.8167,
      image: 'photo-1485833077593-4278bba3f11f',
      description: 'Known for tree-climbing lions and diverse bird species',
      highlights: ['Tree-climbing Lions', 'Hot Springs', 'Pink Flamingos'],
      bestTime: 'November - April',
      rating: 4.6,
      area: '330 km²',
      established: '1960',
      tours: 5
    }
  ];

  const handleDestinationClick = (destination: any) => {
    setSelectedDestination(destination);
  };

  return (
    <section className="py-20 bg-gradient-to-b from-gray-50 to-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%2310b981' fill-opacity='0.1'%3E%3Cpath d='m0 40l40-40v40z'/%3E%3Cpath d='m40 0v40l-40-40z'/%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <Badge className="mb-4 bg-safari-warm-amber/20 text-safari-coffee-brown px-4 py-2">
            <MapPin className="w-4 h-4 mr-2" />
            Destinations
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
            Iconic Tanzania <span className="text-safari-sunset">Destinations</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Explore the most spectacular national parks and conservation areas
            that make Tanzania a premier safari destination in East Africa.
          </p>
        </div>

        {/* Interactive Leaflet Map */}
        <div className="mb-16">
          <LeafletMap
            destinations={destinations}
            onDestinationClick={handleDestinationClick}
            showRoutes={false}
            height="500px"
          />
        </div>

        {/* Selected Destination Details */}
        {selectedDestination && (
          <div className="mb-12 p-6 bg-safari-warm-amber/10 border border-safari-warm-amber/30 rounded-2xl">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-2xl font-bold text-safari-coffee-brown">
                {selectedDestination.name}
              </h3>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setSelectedDestination(null)}
              >
                Close
              </Button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <img
                  src={`https://images.unsplash.com/${selectedDestination.image}?auto=format&fit=crop&w=400&h=300`}
                  alt={selectedDestination.name}
                  className="w-full h-64 object-cover rounded-lg"
                />
              </div>
              <div className="space-y-4">
                <p className="text-gray-700">{selectedDestination.description}</p>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="block text-gray-500">Area</span>
                    <span className="font-semibold">{selectedDestination.area}</span>
                  </div>
                  <div>
                    <span className="block text-gray-500">Established</span>
                    <span className="font-semibold">{selectedDestination.established}</span>
                  </div>
                  <div>
                    <span className="block text-gray-500">Best Time</span>
                    <span className="font-semibold">{selectedDestination.bestTime}</span>
                  </div>
                  <div>
                    <span className="block text-gray-500">Available Tours</span>
                    <span className="font-semibold">{selectedDestination.tours}</span>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button asChild size="sm">
                    <Link to={`/tours?destination=${selectedDestination.id}`}>
                      View Tours
                    </Link>
                  </Button>
                  <Button variant="outline" size="sm" asChild>
                    <Link to="/tour-builder">
                      Plan Custom Tour
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-10 mb-16">
          {destinations.map((destination, index) => (
            <div
              key={index}
              className="group relative overflow-hidden rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 bg-white hover:-translate-y-2 cursor-pointer"
              onClick={() => handleDestinationClick(destination)}
            >
              {/* Background Image */}
              <div className="relative h-80 overflow-hidden">
                <img
                  src={`https://images.unsplash.com/${destination.image}?auto=format&fit=crop&w=800&h=600`}
                  alt={destination.name}
                  className="h-full w-full object-cover group-hover:scale-110 transition-transform duration-700"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent" />
                
                {/* Best Time Badge */}
                <Badge className="absolute top-6 left-6 bg-green-500 text-white px-3 py-1 shadow-lg">
                  <Calendar className="w-3 h-3 mr-1" />
                  {destination.bestTime}
                </Badge>

                {/* Stats Overlay */}
                <div className="absolute top-6 right-6 text-white text-right">
                  <div className="bg-black/40 backdrop-blur-sm rounded-lg p-3">
                    <div className="flex items-center justify-end mb-1">
                      <Star className="h-4 w-4 text-yellow-400 fill-current mr-1" />
                      <span className="font-semibold">{destination.rating}</span>
                    </div>
                    <div className="text-xs opacity-80">{destination.tours} tours available</div>
                  </div>
                </div>
              </div>

              {/* Content Overlay */}
              <div className="absolute inset-0 flex flex-col justify-end p-8 text-white">
                <div className="mb-6">
                  {/* Title */}
                  <h3 className="text-3xl font-bold mb-3 group-hover:text-green-300 transition-colors">
                    {destination.name}
                  </h3>

                  {/* Description */}
                  <p className="text-gray-200 mb-4 leading-relaxed text-lg">
                    {destination.description}
                  </p>

                  {/* Highlights */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {destination.highlights.map((highlight, highlightIndex) => (
                      <Badge
                        key={highlightIndex}
                        variant="outline"
                        className="border-white/30 text-white bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-colors"
                      >
                        {highlight}
                      </Badge>
                    ))}
                  </div>

                  {/* Details */}
                  <div className="grid grid-cols-2 gap-4 text-sm text-gray-300 mb-6">
                    <div>
                      <span className="block text-gray-400">Area</span>
                      <span className="font-semibold">{destination.area}</span>
                    </div>
                    <div>
                      <span className="block text-gray-400">Established</span>
                      <span className="font-semibold">{destination.established}</span>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <Button
                    asChild
                    size="sm"
                    className="bg-green-600 hover:bg-green-700 text-white shadow-lg"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Link to={`/destinations/${destination.id}`}>
                      <MapPin className="mr-2 h-4 w-4" />
                      View Details
                    </Link>
                  </Button>
                  <Button
                    asChild
                    size="sm"
                    variant="outline"
                    className="text-white border-white hover:bg-white hover:text-gray-900 transition-colors"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Link to={`/tours?destination=${destination.id}`}>
                      Explore Tours
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Interactive Planning CTA */}
        <div className="bg-gradient-to-r from-green-600 to-emerald-600 rounded-3xl p-12 text-white text-center relative overflow-hidden">
          <div className="absolute inset-0 opacity-10">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }} />
          </div>
          
          <div className="relative z-10">
            <h3 className="text-4xl font-bold mb-4">Plan Your Perfect Safari Route</h3>
            <p className="text-xl mb-8 max-w-3xl mx-auto opacity-90 leading-relaxed">
              Use our interactive planning tools to explore all destinations and create your ideal safari itinerary 
              with real-time availability, pricing, and expert recommendations.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button 
                asChild
                size="lg"
                className="bg-white text-green-600 hover:bg-gray-100 shadow-lg hover:shadow-xl transition-all"
              >
                <Link to="/tour-builder">
                  <MapPin className="mr-2 h-5 w-5" />
                  Interactive Planner
                </Link>
              </Button>
              <Button 
                asChild
                size="lg"
                variant="outline" 
                className="border-white text-white hover:bg-white hover:text-green-600 transition-colors"
              >
                <Link to="/gallery">
                  <Camera className="mr-2 h-5 w-5" />
                  View Gallery
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DestinationShowcase;
