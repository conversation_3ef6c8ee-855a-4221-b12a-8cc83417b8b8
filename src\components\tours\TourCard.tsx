
import React from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { MapPin, Clock, Users, Star, Calendar } from 'lucide-react';

interface TourCardProps {
  tour: {
    id: string;
    title: string;
    description: string;
    price: number;
    duration: string;
    image: string;
    category: string;
    accommodationLevel: string;
    destinations: string[];
    rating: number;
    reviews: number;
  };
  viewMode?: 'grid' | 'list';
}

const TourCard: React.FC<TourCardProps> = ({ tour, viewMode = 'grid' }) => {
  // Safely handle potentially undefined values
  const destinations = Array.isArray(tour.destinations) ? tour.destinations : [];
  const safeTitle = tour.title || 'Untitled Tour';
  const safeDescription = tour.description || 'No description available';
  const safePrice = tour.price || 0;
  const safeDuration = tour.duration || 'Duration not specified';
  const safeCategory = tour.category || 'Safari';
  const safeAccommodationLevel = tour.accommodationLevel || 'Standard';
  const safeRating = tour.rating || 0;
  const safeReviews = tour.reviews || 0;
  
  // Handle image safely
  const safeImage = tour.image || 'photo-1472396961693-142e6e269027';
  const imageUrl = safeImage.includes('unsplash.com') 
    ? safeImage 
    : `https://images.unsplash.com/${safeImage}?auto=format&fit=crop&w=800&h=500`;
  
  return (
    <Card className="group hover:shadow-xl safari-shadow-hover transition-all duration-300 overflow-hidden">
      <div className="relative">
        <img
          src={imageUrl}
          alt={safeTitle}
          className={`w-full object-cover group-hover:scale-105 transition-transform duration-300 ${
            viewMode === 'grid' ? 'h-48' : 'h-32'
          }`}
        />

        <div className="absolute top-3 left-3 flex flex-col gap-2">
          <Badge className="bg-safari-sunset text-primary-foreground">{safeCategory}</Badge>
          <Badge variant="outline" className="bg-background/90 text-foreground border-safari-warm-amber">{safeAccommodationLevel}</Badge>
        </div>
      </div>

      <CardContent className={`p-${viewMode === 'grid' ? '6' : '4'}`}>
        <div className={`${viewMode === 'list' ? 'flex items-start justify-between' : ''}`}>
          <div className={`${viewMode === 'list' ? 'flex-1 pr-4' : ''}`}>
            <div className="flex items-start justify-between mb-2">
              <h3 className={`font-bold group-hover:text-orange-600 transition-colors ${
                viewMode === 'grid' ? 'text-xl' : 'text-lg'
              }`}>
                {safeTitle}
              </h3>
              {viewMode === 'grid' && (
                <div className="text-right">
                  <div className="text-2xl font-bold text-orange-600">${safePrice.toLocaleString()}</div>
                  <div className="text-sm text-gray-500">per person</div>
                </div>
              )}
            </div>

            <p className={`text-gray-600 mb-4 ${viewMode === 'list' ? 'text-sm' : ''}`}>
              {safeDescription.length > 120 ? `${safeDescription.slice(0, 120)}...` : safeDescription}
            </p>

            {/* Tour Details */}
            <div className={`grid gap-3 mb-4 ${viewMode === 'grid' ? 'grid-cols-2' : 'grid-cols-4'}`}>
              <div className="flex items-center text-sm text-muted-foreground">
                <Clock className="h-4 w-4 mr-2 text-safari-coffee-brown" />
                <span>{safeDuration}</span>
              </div>
              <div className="flex items-center text-sm text-muted-foreground">
                <Users className="h-4 w-4 mr-2 text-safari-coffee-brown" />
                <span>Small Group</span>
              </div>
              <div className="flex items-center text-sm text-muted-foreground">
                <MapPin className="h-4 w-4 mr-2 text-safari-coffee-brown" />
                <span>{destinations.length > 0 ? destinations[0] : 'Tanzania'}</span>
              </div>
              <div className="flex items-center text-sm text-muted-foreground">
                <Star className="h-4 w-4 mr-2 text-safari-golden-sun" />
                <span>{safeRating} ({safeReviews})</span>
              </div>
            </div>
          </div>

          {/* List View Price and Actions */}
          {viewMode === 'list' && (
            <div className="text-right flex flex-col items-end">
              <div className="text-xl font-bold text-orange-600 mb-2">
                ${safePrice.toLocaleString()}
              </div>
              <div className="text-sm text-gray-500 mb-3">per person</div>
              <div className="flex flex-col gap-2">
                <Button asChild size="sm">
                  <Link to={`/tours/${tour.id}`}>View Details</Link>
                </Button>
                <Button asChild variant="outline" size="sm">
                  <Link to={`/book/${tour.id}`}>Book Now</Link>
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Grid View Actions */}
        {viewMode === 'grid' && (
          <div className="flex gap-2">
            <Button asChild className="flex-1">
              <Link to={`/tours/${tour.id}`}>
                View Details
              </Link>
            </Button>
            <Button asChild variant="outline">
              <Link to={`/book/${tour.id}`}>
                <Calendar className="h-4 w-4 mr-2" />
                Book
              </Link>
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TourCard;
