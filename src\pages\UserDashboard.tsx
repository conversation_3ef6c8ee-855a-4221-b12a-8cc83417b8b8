import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { FirebaseService } from '@/services/firebase';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Calendar, MapPin, Heart, User, Settings, FileText, Bell, RefreshCw } from 'lucide-react';
import WishlistButton from '@/components/features/WishlistButton';
import { useWishlist } from '@/contexts/WishlistContext';
import { Booking } from '@/types/firebase';

interface DashboardBooking {
  id: string;
  tourId: string;
  tourTitle: string;
  startDate: string;
  endDate: string;
  duration: string;
  status: 'confirmed' | 'pending' | 'cancelled';
  image: string;
  totalPrice: number;
  guests: number;
  customerName: string;
}

const UserDashboard = () => {
  const { currentUser, userProfile, updateUserProfile } = useAuth();
  const { wishlist } = useWishlist();
  const [activeTab, setActiveTab] = useState('trips');
  const [userBookings, setUserBookings] = useState<DashboardBooking[]>([]);
  const [loading, setLoading] = useState(false);
  const [profileForm, setProfileForm] = useState({
    displayName: userProfile?.displayName || '',
    phone: userProfile?.phone || '',
    country: userProfile?.country || ''
  });

  useEffect(() => {
    if (currentUser) {
      console.log('Current user found, fetching bookings for:', currentUser.uid);
      fetchUserBookings();
    }
    if (userProfile) {
      setProfileForm({
        displayName: userProfile.displayName || '',
        phone: userProfile.phone || '',
        country: userProfile.country || ''
      });
    }
  }, [currentUser, userProfile]);

  const fetchUserBookings = async () => {
    if (!currentUser) return;

    setLoading(true);
    try {
      const bookingsData = await FirebaseService.getUserBookings(currentUser.uid);
      console.log('Raw bookings data:', bookingsData);

      // Transform Firebase booking data to dashboard format
      const transformedBookings: DashboardBooking[] = await Promise.all(
        bookingsData.map(async (booking: Booking) => {
          // Get tour details for image and additional info
          let tourImage = 'https://images.unsplash.com/photo-1516426122078-c23e76319801?auto=format&fit=crop&w=400&h=200';
          let duration = 'Multi-day';

          try {
            if (booking.tourId && !booking.tourId.startsWith('custom-')) {
              const tour = await FirebaseService.getTour(booking.tourId);
              if (tour) {
                tourImage = tour.images?.[0] || tourImage;
                duration = tour.duration || duration;
              }
            }
          } catch (error) {
            console.log('Could not fetch tour details for booking:', booking.id);
          }

          // Calculate duration from dates if not available
          if (booking.bookingDetails?.startDate && booking.bookingDetails?.endDate) {
            const start = new Date(booking.bookingDetails.startDate);
            const end = new Date(booking.bookingDetails.endDate);
            const diffTime = Math.abs(end.getTime() - start.getTime());
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            if (diffDays > 0) {
              duration = `${diffDays} day${diffDays > 1 ? 's' : ''}`;
            }
          }

          return {
            id: booking.id,
            tourId: booking.tourId,
            tourTitle: booking.tourTitle,
            startDate: booking.bookingDetails?.startDate || '',
            endDate: booking.bookingDetails?.endDate || '',
            duration: duration,
            status: booking.status as 'confirmed' | 'pending' | 'cancelled',
            image: tourImage,
            totalPrice: booking.pricing?.totalAmount || 0,
            guests: booking.bookingDetails?.participants || 1,
            customerName: `${booking.customerInfo?.firstName || ''} ${booking.customerInfo?.lastName || ''}`.trim()
          };
        })
      );

      console.log('Transformed bookings:', transformedBookings);
      setUserBookings(transformedBookings);
    } catch (error) {
      console.error('Error fetching bookings:', error);
      setUserBookings([]);
    } finally {
      setLoading(false);
    }
  };

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      await updateUserProfile(profileForm);
      alert('Profile updated successfully!');
    } catch (error) {
      console.error('Error updating profile:', error);
      alert('Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  const upcomingTrips = userBookings.filter(booking => {
    if (!booking.startDate) return false;
    const startDate = new Date(booking.startDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time to start of day
    return (booking.status === 'confirmed' || booking.status === 'pending') && startDate >= today;
  });

  const pastTrips = userBookings.filter(booking => {
    if (!booking.startDate) return false;
    const startDate = new Date(booking.startDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time to start of day
    return booking.status === 'confirmed' && startDate < today;
  });

  const pendingTrips = userBookings.filter(booking => booking.status === 'pending');
  const cancelledTrips = userBookings.filter(booking => booking.status === 'cancelled');

  if (!currentUser) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Please log in to access your dashboard</h2>
          <Button onClick={() => window.location.href = '/login'}>
            Go to Login
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-16">
        <div className="bg-gradient-to-r from-safari-sunset to-safari-deep-rust text-primary-foreground py-8 md:py-12">
          <div className="container mx-auto px-4">
            <h1 className="text-2xl md:text-3xl font-bold mb-2 md:mb-4">Welcome back, {userProfile?.displayName}!</h1>
            <p className="text-lg md:text-xl">Manage your safari adventures</p>
          </div>
        </div>

        <div className="container mx-auto px-4 py-6 md:py-8">
          <div className="max-w-6xl mx-auto">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <div className="overflow-x-auto mb-4">
                <TabsList className="grid w-full min-w-[500px] md:min-w-0 grid-cols-5 h-auto gap-1">
                  <TabsTrigger value="trips" className="text-xs md:text-sm py-2 px-2 md:px-3 flex-col sm:flex-row">
                    <Calendar className="h-3 w-3 md:h-4 md:w-4 mb-1 sm:mb-0 sm:mr-1 md:mr-2" />
                    <span>Trips</span>
                  </TabsTrigger>
                  <TabsTrigger value="wishlist" className="text-xs md:text-sm py-2 px-2 md:px-3 flex-col sm:flex-row">
                    <Heart className="h-3 w-3 md:h-4 md:w-4 mb-1 sm:mb-0 sm:mr-1 md:mr-2" />
                    <span>Wishlist</span>
                  </TabsTrigger>
                  <TabsTrigger value="profile" className="text-xs md:text-sm py-2 px-2 md:px-3 flex-col sm:flex-row">
                    <User className="h-3 w-3 md:h-4 md:w-4 mb-1 sm:mb-0 sm:mr-1 md:mr-2" />
                    <span>Profile</span>
                  </TabsTrigger>
                  <TabsTrigger value="documents" className="text-xs md:text-sm py-2 px-2 md:px-3 flex-col sm:flex-row">
                    <FileText className="h-3 w-3 md:h-4 md:w-4 mb-1 sm:mb-0 sm:mr-1 md:mr-2" />
                    <span>Docs</span>
                  </TabsTrigger>
                  <TabsTrigger value="notifications" className="text-xs md:text-sm py-2 px-2 md:px-3 flex-col sm:flex-row">
                    <Bell className="h-3 w-3 md:h-4 md:w-4 mb-1 sm:mb-0 sm:mr-1 md:mr-2" />
                    <span>Alerts</span>
                  </TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="trips" className="space-y-4 md:space-y-6">
                {/* Debug Info - Remove in production */}
                {process.env.NODE_ENV === 'development' && (
                  <Card className="bg-gray-50 border-dashed">
                    <CardContent className="p-4">
                      <h4 className="font-semibold mb-2">Debug Info:</h4>
                      <p className="text-sm">Total bookings: {userBookings.length}</p>
                      <p className="text-sm">User ID: {currentUser?.uid}</p>
                      <p className="text-sm">Loading: {loading ? 'Yes' : 'No'}</p>
                      {userBookings.length > 0 && (
                        <details className="mt-2">
                          <summary className="text-sm cursor-pointer">Raw booking data</summary>
                          <pre className="text-xs mt-2 overflow-auto max-h-32">
                            {JSON.stringify(userBookings, null, 2)}
                          </pre>
                        </details>
                      )}
                    </CardContent>
                  </Card>
                )}

                {/* All Bookings Summary */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-safari-coffee-brown">{upcomingTrips.length}</div>
                      <div className="text-sm text-muted-foreground">Upcoming</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-safari-golden-sun">{pendingTrips.length}</div>
                      <div className="text-sm text-muted-foreground">Pending</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-safari-sunset">{pastTrips.length}</div>
                      <div className="text-sm text-muted-foreground">Completed</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-safari-deep-rust">{cancelledTrips.length}</div>
                      <div className="text-sm text-muted-foreground">Cancelled</div>
                    </CardContent>
                  </Card>
                </div>

                {/* Upcoming & Pending Trips */}
                <div>
                  <div className="flex justify-between items-center mb-3 md:mb-4">
                    <h2 className="text-xl md:text-2xl font-bold">
                      Upcoming & Pending Trips ({upcomingTrips.length + pendingTrips.length})
                    </h2>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={fetchUserBookings}
                      disabled={loading}
                      className="flex items-center gap-2"
                    >
                      <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                      Refresh
                    </Button>
                  </div>
                  {loading ? (
                    <div className="text-center py-8">
                      <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-400" />
                      <p className="text-gray-600">Loading your bookings...</p>
                    </div>
                  ) : (upcomingTrips.length > 0 || pendingTrips.length > 0) ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                      {[...upcomingTrips, ...pendingTrips].map((trip) => (
                        <Card key={trip.id}>
                          <div className="relative">
                            <img
                              src={trip.image}
                              alt={trip.tourTitle}
                              className="w-full h-40 md:h-48 object-cover rounded-t-lg"
                            />
                            <Badge
                              className={`absolute top-2 right-2 text-xs ${
                                trip.status === 'confirmed' ? 'bg-safari-coffee-brown text-primary-foreground' :
                                trip.status === 'pending' ? 'bg-safari-golden-sun text-primary-foreground' : 'bg-muted text-muted-foreground'
                              }`}
                            >
                              {trip.status}
                            </Badge>
                          </div>
                          <CardContent className="p-3 md:p-4">
                            <h3 className="font-semibold mb-2 text-sm md:text-base line-clamp-2">{trip.tourTitle}</h3>
                            <div className="flex items-center text-xs md:text-sm text-muted-foreground mb-2">
                              <Calendar className="h-3 w-3 md:h-4 md:w-4 mr-1" />
                              <span className="truncate">{trip.startDate} • {trip.duration}</span>
                            </div>
                            <div className="flex items-center text-xs md:text-sm text-muted-foreground mb-2">
                              <User className="h-3 w-3 md:h-4 md:w-4 mr-1" />
                              <span>{trip.guests} guest{trip.guests > 1 ? 's' : ''}</span>
                            </div>
                            <p className="text-base md:text-lg font-bold text-safari-sunset mb-2">${trip.totalPrice}</p>
                            <Button className="w-full mt-2 text-xs md:text-sm">View Details</Button>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <Card>
                      <CardContent className="p-8 text-center">
                        <Calendar className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                        <h3 className="text-lg font-semibold mb-2">No upcoming trips</h3>
                        <p className="text-gray-600 mb-4">Book your next safari adventure</p>
                        <Button onClick={() => window.location.href = '/tours'}>Browse Tours</Button>
                      </CardContent>
                    </Card>
                  )}
                </div>

                <div>
                  <h2 className="text-xl md:text-2xl font-bold mb-3 md:mb-4">Past Trips ({pastTrips.length})</h2>
                  {pastTrips.length > 0 ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                      {pastTrips.map((trip) => (
                        <Card key={trip.id}>
                          <div className="relative">
                            <img
                              src={trip.image}
                              alt={trip.tourTitle}
                              className="w-full h-40 md:h-48 object-cover rounded-t-lg"
                            />
                            <Badge className="absolute top-2 right-2 bg-safari-coffee-brown text-primary-foreground text-xs">
                              {trip.status}
                            </Badge>
                          </div>
                          <CardContent className="p-3 md:p-4">
                            <h3 className="font-semibold mb-2 text-sm md:text-base line-clamp-2">{trip.tourTitle}</h3>
                            <div className="flex items-center text-xs md:text-sm text-muted-foreground mb-2">
                              <Calendar className="h-3 w-3 md:h-4 md:w-4 mr-1" />
                              <span className="truncate">{trip.startDate} • {trip.duration}</span>
                            </div>
                            <div className="flex gap-2">
                              <Button variant="outline" size="sm" className="flex-1 text-xs">Review</Button>
                              <Button variant="outline" size="sm" className="flex-1 text-xs">Rebook</Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <Card>
                      <CardContent className="p-8 text-center">
                        <p className="text-gray-600">No past trips yet</p>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="wishlist" className="space-y-4 md:space-y-6">
                <h2 className="text-xl md:text-2xl font-bold mb-3 md:mb-4">My Wishlist ({wishlist.length})</h2>
                {wishlist.length > 0 ? (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                    {wishlist.map((item) => (
                      <Card key={item.id}>
                        <div className="relative">
                          <img
                            src={`https://images.unsplash.com/${item.image}?auto=format&fit=crop&w=400&h=200`}
                            alt={item.title}
                            className="w-full h-40 md:h-48 object-cover rounded-t-lg"
                          />
                          <Badge className="absolute top-2 right-2 capitalize text-xs">
                            {item.type}
                          </Badge>
                        </div>
                        <CardContent className="p-3 md:p-4">
                          <h3 className="font-semibold mb-2 text-sm md:text-base line-clamp-2">{item.title}</h3>
                          <p className="text-base md:text-lg font-bold text-safari-sunset mb-3">${item.price}</p>
                          <div className="flex gap-2">
                            <Button size="sm" className="flex-1 text-xs">Book Now</Button>
                            <WishlistButton item={item} size="sm" />
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <Card>
                    <CardContent className="p-8 text-center">
                      <Heart className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                      <h3 className="text-lg font-semibold mb-2">Your wishlist is empty</h3>
                      <p className="text-gray-600 mb-4">Start adding tours and destinations you'd like to visit</p>
                      <Button onClick={() => window.location.href = '/tours'}>Explore Tours</Button>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="profile" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Profile Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <form onSubmit={handleProfileUpdate} className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="displayName">Full Name</Label>
                          <Input
                            id="displayName"
                            value={profileForm.displayName}
                            onChange={(e) => setProfileForm({...profileForm, displayName: e.target.value})}
                          />
                        </div>
                        <div>
                          <Label htmlFor="email">Email</Label>
                          <Input
                            id="email"
                            value={currentUser?.email || ''}
                            disabled
                            className="bg-gray-100"
                          />
                        </div>
                        <div>
                          <Label htmlFor="phone">Phone</Label>
                          <Input
                            id="phone"
                            value={profileForm.phone}
                            onChange={(e) => setProfileForm({...profileForm, phone: e.target.value})}
                          />
                        </div>
                        <div>
                          <Label htmlFor="country">Country</Label>
                          <Input
                            id="country"
                            value={profileForm.country}
                            onChange={(e) => setProfileForm({...profileForm, country: e.target.value})}
                          />
                        </div>
                      </div>
                      <Button type="submit" disabled={loading}>
                        {loading ? 'Updating...' : 'Update Profile'}
                      </Button>
                    </form>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="documents" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Travel Documents</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="border rounded-lg p-4">
                        <h4 className="font-semibold mb-2">Passport</h4>
                        <p className="text-sm text-gray-600 mb-2">Upload your passport for visa processing</p>
                        <Button variant="outline" size="sm">Upload Document</Button>
                      </div>
                      <div className="border rounded-lg p-4">
                        <h4 className="font-semibold mb-2">Travel Insurance</h4>
                        <p className="text-sm text-gray-600 mb-2">Required for all safari tours</p>
                        <Button variant="outline" size="sm">Upload Document</Button>
                      </div>
                      <div className="border rounded-lg p-4">
                        <h4 className="font-semibold mb-2">Medical Certificates</h4>
                        <p className="text-sm text-gray-600 mb-2">Yellow fever vaccination required</p>
                        <Button variant="outline" size="sm">Upload Document</Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="notifications" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Notification Preferences</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">Email Notifications</h4>
                        <p className="text-sm text-gray-600">Receive updates about your bookings</p>
                      </div>
                      <input type="checkbox" defaultChecked className="toggle" />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">SMS Notifications</h4>
                        <p className="text-sm text-gray-600">Get text updates for urgent matters</p>
                      </div>
                      <input type="checkbox" defaultChecked className="toggle" />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">Marketing Emails</h4>
                        <p className="text-sm text-gray-600">Special offers and new tour announcements</p>
                      </div>
                      <input type="checkbox" className="toggle" />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default UserDashboard;
