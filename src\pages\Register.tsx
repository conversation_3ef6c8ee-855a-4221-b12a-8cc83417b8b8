
import React from 'react';
import { Link } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import RegisterForm from '@/components/auth/RegisterForm';
import { Camera } from 'lucide-react';

const Register = () => {
  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-16">
        <div className="min-h-screen bg-gradient-to-br from-safari-warm-amber/10 to-safari-sunset/10 flex items-center justify-center py-8 md:py-12 px-4">
          <div className="max-w-md w-full space-y-4 md:space-y-6">
            <div className="text-center">
              <Link to="/" className="inline-flex items-center space-x-2 md:space-x-3 group mb-6 md:mb-8">
                <div className="bg-gradient-to-r from-safari-sunset to-safari-deep-rust text-primary-foreground p-2 md:p-3 rounded-xl group-hover:scale-105 transition-transform">
                  <Camera className="h-6 w-6 md:h-8 md:w-8" />
                </div>
                <div>
                  <span className="font-bold text-2xl md:text-3xl text-foreground">Warrior of Africa Safari</span>
                  <div className="text-xs md:text-sm text-muted-foreground">Adventure Awaits</div>
                </div>
              </Link>
            </div>
            <RegisterForm />
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Register;
