import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Menu, X, User, Heart, Camera, LogOut, Settings } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import AdvancedSearch from '@/components/features/AdvancedSearch';
import { useWishlist } from '@/contexts/WishlistContext';
import { useAuth } from '@/contexts/AuthContext';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const { wishlist } = useWishlist();
  const { currentUser, userProfile, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const handleAuthNavigation = (path: string) => {
    setIsAuthModalOpen(false);
    navigate(path);
  };

  const navItems = [
    { label: 'Tours', href: '/tours' },
    { label: 'Gallery', href: '/gallery' },
    { label: 'About', href: '/about' },
    { label: 'Contact', href: '/contact' },
  ];

  return (
    <header className="fixed top-0 left-0 right-0 z-50 website-gradient-header backdrop-blur-sm border-b border-deep-rust/20 shadow-lg">
      <div className="container mx-auto px-4 max-w-full">
        <div className="flex items-center justify-between h-16 min-w-0">
          {/* Logo with Website-Inspired Styling */}
          <Link to="/" className="flex items-center space-x-1 sm:space-x-2 md:space-x-3 group flex-shrink-0 min-w-0">
            <div className="bg-white/20 backdrop-blur-sm text-white p-2 md:p-3 rounded-xl group-hover:scale-105 group-hover:bg-white/30 transition-all duration-300 shadow-lg flex-shrink-0 border border-white/20">
              <Camera className="h-4 w-4 md:h-6 md:w-6" />
            </div>
            <div className="hidden sm:block min-w-0">
              <span className="font-bold text-base sm:text-lg md:text-xl lg:text-2xl text-white drop-shadow-lg whitespace-nowrap">Warriors of Africa Safari</span>
            </div>
            <div className="sm:hidden min-w-0">
              <span className="font-bold text-xs text-white drop-shadow-lg whitespace-nowrap">Warriors Safari</span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
             <Link
  key={item.href}
  to={item.href}
  className="relative text-white/90 hover:text-white font-medium transition-all duration-300 py-2 group text-xs xl:text-sm drop-shadow-sm"
>
                {item.label}
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-white/80 transition-all duration-300 group-hover:w-full"></span>
              </Link>
            ))}


          </nav>

          {/* Search and Actions */}
          <div className="hidden lg:flex items-center space-x-2 xl:space-x-4 flex-shrink-0">
            <div className="hidden xl:block">
              <AdvancedSearch />
            </div>
            
            {/* Wishlist */}
            {currentUser && (
              <Link to="/user-dashboard">
                <Button variant="ghost" size="sm" className="relative hover:bg-white/20 text-white/90 hover:text-white p-2 backdrop-blur-sm">
                  <Heart className="h-4 w-4 md:h-5 md:w-5" />
                  {wishlist.length > 0 && (
                    <span className="absolute -top-1 -right-1 bg-gradient-to-r from-red-500 to-orange-500 text-white rounded-full text-xs w-4 h-4 md:w-5 md:h-5 flex items-center justify-center shadow-lg">
                      {wishlist.length}
                    </span>
                  )}
                </Button>
              </Link>
            )}

            {/* User Authentication */}
            {currentUser ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="hover:bg-white/20 text-white/90 hover:text-white text-xs md:text-sm px-2 md:px-3 backdrop-blur-sm">
                    <User className="h-4 w-4 mr-1 md:mr-2" />
                    <span className="hidden xl:inline">{userProfile?.displayName || currentUser.email}</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="bg-gradient-to-br from-warm-cream to-light-warm-gray border-deep-rust/20">
                  <DropdownMenuItem asChild>
                    <Link to="/user-dashboard" className="flex items-center text-dark-brown hover:text-warm-orange">
                      <User className="h-4 w-4 mr-2" />
                      Dashboard
                    </Link>
                  </DropdownMenuItem>
                  {userProfile?.role === 'admin' && (
                    <DropdownMenuItem asChild>
                      <Link to="/admin" className="flex items-center text-dark-brown hover:text-warm-orange">
                        <Settings className="h-4 w-4 mr-2" />
                        Admin Panel
                      </Link>
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem onClick={handleLogout} className="flex items-center text-dark-brown hover:text-warm-orange">
                    <LogOut className="h-4 w-4 mr-2" />
                    Logout
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Dialog open={isAuthModalOpen} onOpenChange={setIsAuthModalOpen}>
                <DialogTrigger asChild>
                  <Button className="website-gradient-button text-white shadow-lg hover:shadow-xl rounded-full px-3 md:px-6 text-xs md:text-sm border border-white/20">
                    <User className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
                    <span className="hidden md:inline">Account</span>
                  </Button>
                </DialogTrigger>
                <DialogContent className="bg-gradient-to-br from-warm-cream to-light-warm-gray border-deep-rust/20">
                  <DialogHeader>
                    <DialogTitle className="text-dark-brown text-center text-lg md:text-xl">Join Warrior of Africa Safari</DialogTitle>
                  </DialogHeader>
                  <div className="flex flex-col space-y-4 p-4 md:p-6">
                    <Button
                      onClick={() => handleAuthNavigation('/login')}
                      variant="outline"
                      className="bg-white border-deep-rust/30 text-dark-brown hover:bg-light-warm-gray hover:border-deep-rust/50 transition-all duration-300"
                    >
                      Sign In
                    </Button>
                    <Button
                      onClick={() => handleAuthNavigation('/register')}
                      className="website-gradient-button text-white shadow-lg"
                    >
                      Create Account
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            )}

            <Link to="/tour-builder" className="flex-shrink-0">
              <Button className="website-gradient-button text-white px-2 md:px-4 lg:px-6 py-2 rounded-lg shadow-lg hover:shadow-xl text-xs md:text-sm whitespace-nowrap border border-white/20">
                <span className="hidden md:inline">Plan Safari</span>
                <span className="md:hidden">Plan</span>
              </Button>
            </Link>
          </div>

          {/* Mobile menu button */}
          <button
            className="lg:hidden p-1 md:p-2 rounded-lg hover:bg-white/20 transition-colors text-white/90 hover:text-white flex-shrink-0 backdrop-blur-sm"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? (
              <X className="h-4 w-4 md:h-5 md:w-5" />
            ) : (
              <Menu className="h-4 w-4 md:h-5 md:w-5" />
            )}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden border-t border-deep-rust/20 bg-gradient-to-br from-warm-cream to-light-warm-gray backdrop-blur-md py-4 md:py-6 rounded-b-xl">
            <nav className="flex flex-col space-y-3 md:space-y-4">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  to={item.href}
                  className="text-dark-brown hover:text-warm-orange font-medium transition-colors px-4 py-2 rounded-lg hover:bg-light-warm-gray text-sm md:text-base"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.label}
                </Link>
              ))}
              <div className="px-4 pt-3 md:pt-4 border-t border-deep-rust/20 space-y-3 md:space-y-4">
                <AdvancedSearch />
                <div className="flex flex-col space-y-3">
                  {currentUser ? (
                    <div className="space-y-2">
                      <Link to="/user-dashboard" className="block">
                        <Button variant="ghost" size="sm" className="w-full hover:bg-light-warm-gray text-dark-brown text-sm justify-start">
                          <User className="h-4 w-4 mr-2" />
                          Dashboard
                        </Button>
                      </Link>
                      {userProfile?.role === 'admin' && (
                        <Link to="/admin" className="block">
                          <Button variant="ghost" size="sm" className="w-full hover:bg-light-warm-gray text-dark-brown text-sm justify-start">
                            <Settings className="h-4 w-4 mr-2" />
                            Admin Panel
                          </Button>
                        </Link>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleLogout}
                        className="w-full text-dark-brown hover:bg-light-warm-gray text-sm justify-start"
                      >
                        <LogOut className="h-4 w-4 mr-2" />
                        Logout
                      </Button>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2 w-full">
                      <Link to="/login" className="flex-1">
                        <Button variant="outline" size="sm" className="w-full border-deep-rust/30 text-dark-brown hover:bg-light-warm-gray text-sm">Sign In</Button>
                      </Link>
                      <Link to="/register" className="flex-1">
                        <Button size="sm" className="w-full website-gradient-button text-white text-sm">Sign Up</Button>
                      </Link>
                    </div>
                  )}
                </div>
                <Link to="/tour-builder" className="block">
                  <Button className="w-full website-gradient-button text-white text-sm">
                    Plan Safari
                  </Button>
                </Link>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
