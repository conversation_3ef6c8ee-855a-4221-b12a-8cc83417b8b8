@import url('https://fonts.googleapis.com/css2?family=Bubblegum+Sans&display=swap');
@import 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Safari Color Palette - Light Theme */
    --background: 48 100% 96%; /* Cream background */
    --foreground: 25 45% 15%; /* Dark chocolate text */
    --card: 48 100% 96%; /* Cream cards */
    --card-foreground: 25 45% 15%; /* Dark chocolate card text */
    --popover: 48 100% 96%; /* Cream popover */
    --popover-foreground: 25 45% 15%; /* Dark chocolate popover text */
    --primary: 22 100% 63%; /* Safari sunset orange */
    --primary-foreground: 48 100% 96%; /* Cream text on primary */
    --secondary: 39 100% 50%; /* Warm amber */
    --secondary-foreground: 25 45% 15%; /* Dark chocolate on secondary */
    --muted: 39 44% 85%; /* Warm beige muted */
    --muted-foreground: 30 25% 45%; /* Coffee brown muted text */
    --accent: 45 100% 50%; /* Golden yellow accent */
    --accent-foreground: 25 45% 15%; /* Dark chocolate on accent */
    --destructive: 0 47% 57%; /* Error red */
    --destructive-foreground: 48 100% 96%; /* Cream on destructive */
    --border: 39 44% 85%; /* Warm beige borders */
    --input: 39 44% 85%; /* Warm beige input backgrounds */
    --ring: 22 100% 63%; /* Safari sunset orange focus ring */
    --radius: 0.5rem;
    --sidebar-background: 48 100% 96%; /* Cream sidebar */
    --sidebar-foreground: 25 45% 15%; /* Dark chocolate sidebar text */
    --sidebar-primary: 25 45% 15%; /* Dark chocolate sidebar primary */
    --sidebar-primary-foreground: 48 100% 96%; /* Cream sidebar primary text */
    --sidebar-accent: 39 44% 85%; /* Warm beige sidebar accent */
    --sidebar-accent-foreground: 25 45% 15%; /* Dark chocolate sidebar accent text */
    --sidebar-border: 39 44% 85%; /* Warm beige sidebar border */
    --sidebar-ring: 22 100% 63%; /* Safari sunset orange sidebar ring */

    /* Additional Safari Colors */
    --safari-sunset: 22 100% 63%; /* #FF8C42 */
    --golden-sun: 51 100% 50%; /* #FFD700 */
    --deep-safari-brown: 25 45% 15%; /* #8B4513 */
    --warm-amber: 39 100% 50%; /* #FFA500 */
    --burnt-sienna: 19 50% 40%; /* #A0522D */
    --dusty-rose: 0 25% 69%; /* #BC8F8F */
    --savanna-gold: 43 74% 49%; /* #DAA520 */
    --coffee-brown: 30 25% 45%; /* #6F4E37 */
    --bright-orange: 16 100% 59%; /* #FF6B35 */
    --golden-yellow: 32 100% 64%; /* #FFB347 */
    --deep-rust: 22 82% 39%; /* #B7410E */
  }

  .dark {
    /* Safari Color Palette - Dark Theme */
    --background: 25 45% 8%; /* Very dark chocolate background */
    --foreground: 48 100% 96%; /* Cream text */
    --card: 25 45% 12%; /* Dark chocolate cards */
    --card-foreground: 48 100% 96%; /* Cream card text */
    --popover: 25 45% 12%; /* Dark chocolate popover */
    --popover-foreground: 48 100% 96%; /* Cream popover text */
    --primary: 22 100% 63%; /* Safari sunset orange (same as light) */
    --primary-foreground: 25 45% 8%; /* Very dark chocolate on primary */
    --secondary: 30 25% 25%; /* Darker coffee brown */
    --secondary-foreground: 48 100% 96%; /* Cream on secondary */
    --muted: 30 25% 20%; /* Dark coffee brown muted */
    --muted-foreground: 39 44% 70%; /* Light beige muted text */
    --accent: 45 100% 50%; /* Golden yellow accent (same as light) */
    --accent-foreground: 25 45% 8%; /* Very dark chocolate on accent */
    --destructive: 0 47% 57%; /* Error red (same as light) */
    --destructive-foreground: 48 100% 96%; /* Cream on destructive */
    --border: 30 25% 20%; /* Dark coffee brown borders */
    --input: 30 25% 20%; /* Dark coffee brown input backgrounds */
    --ring: 22 100% 63%; /* Safari sunset orange focus ring */
    --sidebar-background: 25 45% 10%; /* Very dark chocolate sidebar */
    --sidebar-foreground: 48 100% 96%; /* Cream sidebar text */
    --sidebar-primary: 22 100% 63%; /* Safari sunset orange sidebar primary */
    --sidebar-primary-foreground: 25 45% 8%; /* Very dark chocolate sidebar primary text */
    --sidebar-accent: 30 25% 20%; /* Dark coffee brown sidebar accent */
    --sidebar-accent-foreground: 48 100% 96%; /* Cream sidebar accent text */
    --sidebar-border: 30 25% 20%; /* Dark coffee brown sidebar border */
    --sidebar-ring: 22 100% 63%; /* Safari sunset orange sidebar ring */

    /* Additional Safari Colors - Dark Theme Variants */
    --safari-sunset: 22 100% 63%; /* #FF8C42 (same) */
    --golden-sun: 51 100% 50%; /* #FFD700 (same) */
    --deep-safari-brown: 25 45% 8%; /* Darker for dark theme */
    --warm-amber: 39 100% 45%; /* Slightly darker amber */
    --burnt-sienna: 19 50% 35%; /* Darker sienna */
    --dusty-rose: 0 25% 60%; /* Darker dusty rose */
    --savanna-gold: 43 74% 44%; /* Darker savanna gold */
    --coffee-brown: 30 25% 25%; /* Darker coffee brown */
    --bright-orange: 16 100% 54%; /* Slightly darker bright orange */
    --golden-yellow: 32 100% 59%; /* Slightly darker golden yellow */
    --deep-rust: 22 82% 34%; /* Darker rust */
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    /* Prevent horizontal scroll on mobile */
    overflow-x: hidden;
  }

  /* Ensure full height on mobile devices */
  html, body {
    height: 100%;
    min-height: 100vh;
    /* Fix iOS Safari viewport height issues */
    min-height: -webkit-fill-available;
  }
}

/* Mobile-first responsive utilities */
@layer utilities {
  /* Touch targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Mobile-friendly spacing */
  .mobile-spacing {
    @apply px-4 sm:px-6 md:px-8;
  }

  /* Responsive text sizes */
  .text-responsive-xs {
    @apply text-xs sm:text-sm;
  }

  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl md:text-2xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl md:text-3xl;
  }

  .text-responsive-2xl {
    @apply text-2xl sm:text-3xl md:text-4xl;
  }

  .text-responsive-3xl {
    @apply text-3xl sm:text-4xl md:text-5xl;
  }

  /* Mobile navigation fixes */
  .mobile-nav-safe {
    /* Account for mobile browser UI */
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Prevent zoom on input focus (iOS Safari) */
  .no-zoom {
    font-size: 16px;
  }

  /* Mobile-friendly button spacing */
  .mobile-button {
    @apply px-4 py-3 text-base;
  }

  @screen sm {
    .mobile-button {
      @apply px-6 py-2 text-sm;
    }
  }

  /* Responsive grid utilities */
  .grid-responsive-1 {
    @apply grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }

  .grid-responsive-2 {
    @apply grid-cols-1 md:grid-cols-2;
  }

  .grid-responsive-3 {
    @apply grid-cols-1 md:grid-cols-2 lg:grid-cols-3;
  }

  /* Mobile-first card spacing */
  .card-mobile {
    @apply p-4 sm:p-6;
  }

  /* Safari Theme Utilities */
  .safari-gradient-sunset {
    @apply bg-gradient-to-r from-safari-sunset to-safari-bright-orange;
  }

  .safari-gradient-earth {
    @apply bg-gradient-to-br from-safari-coffee-brown via-safari-burnt-sienna to-safari-deep-brown;
  }

  .safari-gradient-golden {
    @apply bg-gradient-to-r from-safari-golden-sun to-safari-savanna-gold;
  }

  .safari-text-gradient {
    @apply bg-gradient-to-r from-safari-sunset to-safari-deep-rust bg-clip-text text-transparent;
  }

  .safari-shadow {
    box-shadow: 0 4px 20px rgba(255, 140, 66, 0.15);
  }

  .safari-shadow-hover:hover {
    box-shadow: 0 8px 30px rgba(255, 140, 66, 0.25);
  }
}

/* Animation Keyframes */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Animation Classes */
.animate-float {
  animation: float 5s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 3s ease-in-out infinite;
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-slide-in-up {
  animation: slideInUp 0.6s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.4s ease-out forwards;
}

/* Staggered Animation Delays */
.animate-delay-100 {
  animation-delay: 100ms;
}

.animate-delay-200 {
  animation-delay: 200ms;
}

.animate-delay-300 {
  animation-delay: 300ms;
}

.animate-delay-400 {
  animation-delay: 400ms;
}

.animate-delay-500 {
  animation-delay: 500ms;
}

/* Parallax Background */
.parallax-bg {
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

/* Mobile parallax fix */
@media (max-width: 768px) {
  .parallax-bg {
    background-attachment: scroll;
  }
}

/* Seamless Transitions */
.page-transition {
  transition: all 0.3s ease-in-out;
}

.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Mobile hover states - disable on touch devices */
@media (hover: none) and (pointer: coarse) {
  .hover-scale:hover {
    transform: none;
  }
}

/* Improved Focus States */
*:focus-visible {
  outline: 2px solid #f97316;
  outline-offset: 2px;
}

/* Typography Enhancements */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Inter', sans-serif;
  line-height: 1.2;
}

/* Mobile-specific optimizations */
@media (max-width: 640px) {
  /* Prevent text selection on touch */
  .no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Better touch feedback */
  button, a {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  }

  /* Improve scrolling on iOS */
  * {
    -webkit-overflow-scrolling: touch;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* Sharper borders */
  .border {
    border-width: 0.5px;
  }
}

/* Loading states for better UX */
.skeleton {
  @apply animate-pulse bg-muted rounded;
}

.loading-overlay {
  @apply fixed inset-0 bg-safari-deep-brown bg-opacity-50 flex items-center justify-center z-50;
}

/* Accessible focus indicators */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2;
}

/* Mobile-safe sticky positioning */
.sticky-mobile {
  position: -webkit-sticky;
  position: sticky;
}

/* Marquee animation improvements */
.animate-marquee {
  animation-duration: var(--duration, 40s);
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  will-change: transform;
}

/* Ensure smooth marquee performance */
@media (prefers-reduced-motion: reduce) {
  .animate-marquee {
    animation-play-state: paused;
  }
}
