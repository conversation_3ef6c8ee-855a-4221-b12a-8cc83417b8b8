@import url('https://fonts.googleapis.com/css2?family=Bubblegum+Sans&display=swap');
@import 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Website-Inspired Color Palette - Light Theme */
    --background: 48 85% 96%; /* Warm cream background #FDF6E3 */
    --foreground: 25 60% 10%; /* Dark brown text #2C1810 */
    --card: 48 75% 94%; /* Light warm gray cards #F8F5F0 */
    --card-foreground: 25 60% 10%; /* Dark brown card text */
    --popover: 48 75% 94%; /* Light warm gray popover */
    --popover-foreground: 25 60% 10%; /* Dark brown popover text */
    --primary: 16 65% 47%; /* Deep rust/terracotta #B85C38 */
    --primary-foreground: 0 0% 100%; /* White text on primary */
    --secondary: 28 75% 52%; /* Warm orange #E67E22 */
    --secondary-foreground: 0 0% 100%; /* White on secondary */
    --muted: 48 60% 90%; /* Light beige muted */
    --muted-foreground: 25 40% 35%; /* Medium brown muted text #5D4037 */
    --accent: 32 85% 54%; /* Golden orange #F39C12 */
    --accent-foreground: 25 60% 10%; /* Dark brown on accent */
    --destructive: 14 85% 47%; /* Warm red #D84315 */
    --destructive-foreground: 0 0% 100%; /* White on destructive */
    --border: 48 50% 85%; /* Light warm borders */
    --input: 48 50% 92%; /* Light input backgrounds */
    --ring: 16 65% 47%; /* Deep rust focus ring */
    --radius: 0.5rem;
    --sidebar-background: 48 85% 96%; /* Warm cream sidebar */
    --sidebar-foreground: 25 60% 10%; /* Dark brown sidebar text */
    --sidebar-primary: 16 65% 47%; /* Deep rust sidebar primary */
    --sidebar-primary-foreground: 0 0% 100%; /* White sidebar primary text */
    --sidebar-accent: 48 60% 90%; /* Light beige sidebar accent */
    --sidebar-accent-foreground: 25 60% 10%; /* Dark brown sidebar accent text */
    --sidebar-border: 48 50% 85%; /* Light warm sidebar border */
    --sidebar-ring: 16 65% 47%; /* Deep rust sidebar ring */

    /* Website-Inspired Color Variables */
    --deep-rust: 16 65% 47%; /* #B85C38 - Primary brand color */
    --warm-orange: 28 75% 52%; /* #E67E22 - Secondary brand color */
    --golden-orange: 32 85% 54%; /* #F39C12 - Accent color */
    --warm-cream: 48 85% 96%; /* #FDF6E3 - Background */
    --light-warm-gray: 48 75% 94%; /* #F8F5F0 - Card backgrounds */
    --deep-brown: 25 45% 27%; /* #8B4513 - Footer/dark sections */
    --dark-brown: 25 60% 10%; /* #2C1810 - Primary text */
    --medium-brown: 25 40% 35%; /* #5D4037 - Secondary text */
    --bright-orange: 22 100% 59%; /* #FF8C42 - Highlights */
    --golden-yellow: 32 100% 64%; /* #FFB347 - Secondary accents */
    --warm-red: 14 85% 47%; /* #D84315 - Emphasis */
  }

  .dark {
    /* Website-Inspired Color Palette - Dark Theme */
    --background: 25 45% 8%; /* Very dark brown background */
    --foreground: 48 85% 96%; /* Warm cream text */
    --card: 25 45% 12%; /* Dark brown cards */
    --card-foreground: 48 85% 96%; /* Warm cream card text */
    --popover: 25 45% 12%; /* Dark brown popover */
    --popover-foreground: 48 85% 96%; /* Warm cream popover text */
    --primary: 16 65% 52%; /* Lighter rust for dark theme */
    --primary-foreground: 0 0% 100%; /* White on primary */
    --secondary: 28 75% 57%; /* Lighter warm orange */
    --secondary-foreground: 25 45% 8%; /* Dark brown on secondary */
    --muted: 25 30% 18%; /* Dark muted */
    --muted-foreground: 48 60% 75%; /* Light muted text */
    --accent: 32 85% 59%; /* Lighter golden orange */
    --accent-foreground: 25 45% 8%; /* Dark brown on accent */
    --destructive: 14 85% 52%; /* Lighter warm red */
    --destructive-foreground: 0 0% 100%; /* White on destructive */
    --border: 25 30% 18%; /* Dark borders */
    --input: 25 30% 15%; /* Dark input backgrounds */
    --ring: 16 65% 52%; /* Lighter rust focus ring */
    --sidebar-background: 25 45% 10%; /* Very dark brown sidebar */
    --sidebar-foreground: 48 85% 96%; /* Warm cream sidebar text */
    --sidebar-primary: 16 65% 52%; /* Lighter rust sidebar primary */
    --sidebar-primary-foreground: 0 0% 100%; /* White sidebar primary text */
    --sidebar-accent: 25 30% 18%; /* Dark sidebar accent */
    --sidebar-accent-foreground: 48 85% 96%; /* Warm cream sidebar accent text */
    --sidebar-border: 25 30% 18%; /* Dark sidebar border */
    --sidebar-ring: 16 65% 52%; /* Lighter rust sidebar ring */

    /* Website-Inspired Color Variables - Dark Theme */
    --deep-rust: 16 65% 52%; /* Lighter rust for dark theme */
    --warm-orange: 28 75% 57%; /* Lighter warm orange */
    --golden-orange: 32 85% 59%; /* Lighter golden orange */
    --warm-cream: 48 85% 96%; /* Same warm cream */
    --light-warm-gray: 25 30% 15%; /* Dark gray for cards */
    --deep-brown: 25 45% 8%; /* Very dark brown */
    --dark-brown: 25 45% 8%; /* Very dark brown */
    --medium-brown: 25 30% 25%; /* Medium dark brown */
    --bright-orange: 22 100% 64%; /* Brighter orange for dark theme */
    --golden-yellow: 32 100% 69%; /* Brighter golden yellow */
    --warm-red: 14 85% 52%; /* Lighter warm red */
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    /* Prevent horizontal scroll on mobile */
    overflow-x: hidden;
  }

  /* Ensure full height on mobile devices */
  html, body {
    height: 100%;
    min-height: 100vh;
    /* Fix iOS Safari viewport height issues */
    min-height: -webkit-fill-available;
  }
}

/* Mobile-first responsive utilities */
@layer utilities {
  /* Touch targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Mobile-friendly spacing */
  .mobile-spacing {
    @apply px-4 sm:px-6 md:px-8;
  }

  /* Responsive text sizes */
  .text-responsive-xs {
    @apply text-xs sm:text-sm;
  }

  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl md:text-2xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl md:text-3xl;
  }

  .text-responsive-2xl {
    @apply text-2xl sm:text-3xl md:text-4xl;
  }

  .text-responsive-3xl {
    @apply text-3xl sm:text-4xl md:text-5xl;
  }

  /* Mobile navigation fixes */
  .mobile-nav-safe {
    /* Account for mobile browser UI */
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Prevent zoom on input focus (iOS Safari) */
  .no-zoom {
    font-size: 16px;
  }

  /* Mobile-friendly button spacing */
  .mobile-button {
    @apply px-4 py-3 text-base;
  }

  @screen sm {
    .mobile-button {
      @apply px-6 py-2 text-sm;
    }
  }

  /* Responsive grid utilities */
  .grid-responsive-1 {
    @apply grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }

  .grid-responsive-2 {
    @apply grid-cols-1 md:grid-cols-2;
  }

  .grid-responsive-3 {
    @apply grid-cols-1 md:grid-cols-2 lg:grid-cols-3;
  }

  /* Mobile-first card spacing */
  .card-mobile {
    @apply p-4 sm:p-6;
  }

  /* Website-Inspired Theme Utilities */
  .website-gradient-header {
    background: linear-gradient(135deg, #B85C38 0%, #A0522D 35%, #8B4513 70%, #6B4423 100%);
  }

  .website-gradient-footer {
    background: linear-gradient(45deg, #6B4423 0%, #8B4513 30%, #A0522D 65%, #B85C38 100%);
  }

  .website-gradient-primary {
    background: linear-gradient(135deg, #B85C38 0%, #A0522D 50%, #8B4513 100%);
  }

  .website-gradient-warm {
    background: linear-gradient(135deg, #E67E22 0%, #D84315 35%, #B85C38 70%, #A0522D 100%);
  }

  .website-gradient-golden {
    background: linear-gradient(135deg, #F39C12 0%, #E67E22 50%, #D84315 100%);
  }

  .website-gradient-button {
    background: linear-gradient(135deg, #B85C38 0%, #A0522D 100%);
    transition: all 0.3s ease;
  }

  .website-gradient-button:hover {
    background: linear-gradient(135deg, #D84315 0%, #B85C38 100%);
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(184, 92, 56, 0.3);
  }

  .website-text-gradient {
    background: linear-gradient(135deg, #B85C38 0%, #8B4513 50%, #6B4423 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .website-shadow {
    box-shadow: 0 4px 20px rgba(184, 92, 56, 0.15);
  }

  .website-shadow-hover:hover {
    box-shadow: 0 8px 30px rgba(184, 92, 56, 0.25);
  }

  /* Card gradient effects */
  .website-gradient-card {
    background: linear-gradient(135deg, rgba(253, 246, 227, 0.95) 0%, rgba(248, 245, 240, 0.95) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(184, 92, 56, 0.1);
  }

  .website-gradient-card-hover:hover {
    background: linear-gradient(135deg, rgba(253, 246, 227, 1) 0%, rgba(248, 245, 240, 1) 100%);
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(184, 92, 56, 0.15);
  }

  /* Section background gradients */
  .website-gradient-section-light {
    background: linear-gradient(135deg, #FDF6E3 0%, #F8F5F0 50%, #FDF6E3 100%);
  }

  .website-gradient-section-warm {
    background: linear-gradient(135deg, rgba(184, 92, 56, 0.05) 0%, rgba(160, 82, 45, 0.05) 50%, rgba(139, 69, 19, 0.05) 100%);
  }

  /* Legacy safari classes for backward compatibility */
  .safari-gradient-sunset {
    @apply website-gradient-primary;
  }

  .safari-gradient-earth {
    @apply website-gradient-warm;
  }

  .safari-gradient-golden {
    @apply website-gradient-golden;
  }

  .safari-text-gradient {
    @apply website-text-gradient;
  }

  .safari-shadow {
    @apply website-shadow;
  }

  .safari-shadow-hover:hover {
    @apply website-shadow-hover;
  }
}

/* Animation Keyframes */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Animation Classes */
.animate-float {
  animation: float 5s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 3s ease-in-out infinite;
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-slide-in-up {
  animation: slideInUp 0.6s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.4s ease-out forwards;
}

/* Staggered Animation Delays */
.animate-delay-100 {
  animation-delay: 100ms;
}

.animate-delay-200 {
  animation-delay: 200ms;
}

.animate-delay-300 {
  animation-delay: 300ms;
}

.animate-delay-400 {
  animation-delay: 400ms;
}

.animate-delay-500 {
  animation-delay: 500ms;
}

/* Parallax Background */
.parallax-bg {
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

/* Mobile parallax fix */
@media (max-width: 768px) {
  .parallax-bg {
    background-attachment: scroll;
  }
}

/* Seamless Transitions */
.page-transition {
  transition: all 0.3s ease-in-out;
}

.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Mobile hover states - disable on touch devices */
@media (hover: none) and (pointer: coarse) {
  .hover-scale:hover {
    transform: none;
  }
}

/* Improved Focus States */
*:focus-visible {
  outline: 2px solid #f97316;
  outline-offset: 2px;
}

/* Typography Enhancements */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Inter', sans-serif;
  line-height: 1.2;
}

/* Mobile-specific optimizations */
@media (max-width: 640px) {
  /* Prevent text selection on touch */
  .no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Better touch feedback */
  button, a {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  }

  /* Improve scrolling on iOS */
  * {
    -webkit-overflow-scrolling: touch;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* Sharper borders */
  .border {
    border-width: 0.5px;
  }
}

/* Loading states for better UX */
.skeleton {
  @apply animate-pulse bg-muted rounded;
}

.loading-overlay {
  @apply fixed inset-0 bg-safari-deep-brown bg-opacity-50 flex items-center justify-center z-50;
}

/* Accessible focus indicators */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2;
}

/* Mobile-safe sticky positioning */
.sticky-mobile {
  position: -webkit-sticky;
  position: sticky;
}

/* Marquee animation improvements */
.animate-marquee {
  animation-duration: var(--duration, 40s);
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  will-change: transform;
}

/* Ensure smooth marquee performance */
@media (prefers-reduced-motion: reduce) {
  .animate-marquee {
    animation-play-state: paused;
  }
}
