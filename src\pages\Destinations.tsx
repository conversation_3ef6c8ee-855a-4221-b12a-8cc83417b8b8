import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Link } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { MapPin, Star, Clock, Camera, Search, Filter } from 'lucide-react';
import { FirebaseService } from '@/services/firebase';
import { Destination } from '@/types/firebase';
import { useQuery } from '@tanstack/react-query';

const Destinations = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');

  // Use React Query for better caching and performance
  const { data: destinations = [], isLoading: loading, error } = useQuery({
    queryKey: ['destinations'],
    queryFn: () => FirebaseService.getDestinations(),
    staleTime: 10 * 60 * 1000, // 10 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
  });

  // Memoize filtered destinations to prevent unnecessary recalculations
  const filteredDestinations = useMemo(() => {
    let filtered = destinations;

    // Search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase().trim();
      filtered = filtered.filter(dest =>
        dest.name?.toLowerCase().includes(term) ||
        dest.description?.toLowerCase().includes(term) ||
        dest.region?.toLowerCase().includes(term)
      );
    }

    // Category filter
    if (selectedFilter !== 'all') {
      filtered = filtered.filter(dest => {
        switch (selectedFilter) {
          case 'featured':
            return dest.featured;
          case 'national-parks':
            return dest.name?.toLowerCase().includes('national park');
          case 'conservation-areas':
            return dest.name?.toLowerCase().includes('conservation') ||
                   dest.name?.toLowerCase().includes('crater');
          default:
            return true;
        }
      });
    }

    return filtered;
  }, [destinations, searchTerm, selectedFilter]);

  // Debounced search to improve performance
  const debouncedSearchTerm = useMemo(() => {
    const timeoutId = setTimeout(() => searchTerm, 300);
    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  // Handle search input with debouncing
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  }, []);

  // Handle filter change
  const handleFilterChange = useCallback((filter: string) => {
    setSelectedFilter(filter);
  }, []);

  const filters = [
    { value: 'all', label: 'All Destinations' },
    { value: 'featured', label: 'Featured' },
    { value: 'national-parks', label: 'National Parks' },
    { value: 'conservation-areas', label: 'Conservation Areas' }
  ];

  if (loading) {
    return (
      <div className="min-h-screen">
        <Header />
        <main className="pt-16">
          <div className="bg-gradient-to-r from-green-600 to-blue-600 text-white py-16">
            <div className="container mx-auto px-4">
              <Skeleton className="h-12 w-96 mb-4 bg-white/20" />
              <Skeleton className="h-6 w-full max-w-2xl bg-white/20" />
            </div>
          </div>
          <div className="container mx-auto px-4 py-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <Card key={i} className="overflow-hidden">
                  <Skeleton className="h-48 w-full" />
                  <CardContent className="p-6">
                    <Skeleton className="h-6 w-3/4 mb-2" />
                    <Skeleton className="h-4 w-full mb-4" />
                    <div className="flex justify-between items-center">
                      <Skeleton className="h-4 w-20" />
                      <Skeleton className="h-8 w-24" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-16">
        {/* Hero Section */}
        <div
          className="relative text-white py-16 overflow-hidden bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: 'url("https://qconvxhtavuzjdwuowpt.supabase.co/storage/v1/object/public/photoz/image%20(5).png")'
          }}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-safari-sunset/80 to-safari-deep-rust/80" />
          <div className="relative z-10 container mx-auto px-4">
            <div className="text-center">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                Discover Tanzania's <span className="text-safari-golden-sun">Iconic Destinations</span>
              </h1>
              <p className="text-xl max-w-3xl mx-auto leading-relaxed">
                Explore the most spectacular national parks, conservation areas, and wildlife sanctuaries
                that make Tanzania a premier safari destination in East Africa.
              </p>
            </div>
          </div>
        </div>

        {/* Search and Filter Section */}
        <div className="bg-white border-b py-6">
          <div className="container mx-auto px-4">
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search destinations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2 flex-wrap">
                {filters.map((filter) => (
                  <Button
                    key={filter.value}
                    variant={selectedFilter === filter.value ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedFilter(filter.value)}
                    className="whitespace-nowrap"
                  >
                    <Filter className="h-4 w-4 mr-2" />
                    {filter.label}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Destinations Grid */}
        <div className="container mx-auto px-4 py-12">
          {filteredDestinations.length === 0 ? (
            <div className="text-center py-12">
              <MapPin className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-600 mb-2">No destinations found</h3>
              <p className="text-gray-500">Try adjusting your search or filter criteria.</p>
            </div>
          ) : (
            <>
              <div className="text-center mb-8">
                <h2 className="text-2xl md:text-3xl font-bold mb-2">
                  {filteredDestinations.length} Amazing Destination{filteredDestinations.length !== 1 ? 's' : ''}
                </h2>
                <p className="text-gray-600">
                  Each destination offers unique wildlife experiences and breathtaking landscapes
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredDestinations.map((destination) => (
                  <Card key={destination.id} className="overflow-hidden hover:shadow-xl transition-all duration-300 group">
                    <div className="relative h-48 overflow-hidden">
                      <img
                        src={destination.images?.[0] ? 
                          `https://images.unsplash.com/${destination.images[0]}?auto=format&fit=crop&w=600&h=400` :
                          `https://images.unsplash.com/photo-1472396961693-142e6e269027?auto=format&fit=crop&w=600&h=400`
                        }
                        alt={destination.name}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
                      {destination.featured && (
                        <Badge className="absolute top-4 left-4 bg-yellow-500 text-black">
                          <Star className="h-3 w-3 mr-1" />
                          Featured
                        </Badge>
                      )}
                      <div className="absolute bottom-4 left-4 right-4">
                        <h3 className="text-white font-bold text-lg mb-1">{destination.name}</h3>
                        <p className="text-white/90 text-sm flex items-center">
                          <MapPin className="h-3 w-3 mr-1" />
                          {destination.region}, {destination.country}
                        </p>
                      </div>
                    </div>
                    
                    <CardContent className="p-6">
                      <p className="text-gray-600 mb-4 line-clamp-3">
                        {destination.description}
                      </p>
                      
                      <div className="space-y-3 mb-4">
                        <div className="flex items-center text-sm text-gray-500">
                          <Clock className="h-4 w-4 mr-2" />
                          Best Time: {destination.bestTimeToVisit?.join(', ') || 'Year Round'}
                        </div>
                        <div className="flex items-center text-sm text-gray-500">
                          <Camera className="h-4 w-4 mr-2" />
                          {destination.wildlife?.length || 0} Wildlife Species
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-1 mb-4">
                        {destination.activities?.slice(0, 3).map((activity, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {activity}
                          </Badge>
                        ))}
                        {destination.activities && destination.activities.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{destination.activities.length - 3} more
                          </Badge>
                        )}
                      </div>

                      <Button asChild className="w-full">
                        <Link to={`/destinations/${destination.id}`}>
                          Explore Destination
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Destinations;
