
import React from 'react';
import { MapPin, Phone, Mail, Facebook, Instagram, Youtube } from 'lucide-react';
import NewsletterSignup from '@/components/features/NewsletterSignup';

const Footer = () => {
  return (
    <footer className="bg-deep-brown text-warm-cream">
      <div className="container mx-auto px-4 py-8 md:py-12">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 mb-6 md:mb-8">
          {/* Company Info */}
          <div className="sm:col-span-2 lg:col-span-1">
            <div className="flex items-center space-x-2 mb-4">
              <div className="h-6 w-6 md:h-8 md:w-8 rounded-full bg-gradient-to-r from-deep-rust to-warm-orange flex items-center justify-center">
                <MapPin className="h-3 w-3 md:h-4 md:w-4 text-white" />
              </div>
              <span className="text-lg md:text-xl font-bold">Warrior of Africa Safari</span>
            </div>
            <p className="text-warm-cream/80 mb-4 leading-relaxed text-sm md:text-base">
              Creating unforgettable safari experiences in Tanzania for over 15 years.
              Your gateway to authentic African adventures.
            </p>
            <div className="flex space-x-3 md:space-x-4">
              <button className="text-warm-cream/60 hover:text-golden-orange transition-colors p-1">
                <Facebook className="h-4 w-4 md:h-5 md:w-5" />
              </button>
              <button className="text-warm-cream/60 hover:text-golden-orange transition-colors p-1">
                <Instagram className="h-4 w-4 md:h-5 md:w-5" />
              </button>
              <button className="text-warm-cream/60 hover:text-golden-orange transition-colors p-1">
                <Youtube className="h-4 w-4 md:h-5 md:w-5" />
              </button>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-base md:text-lg font-semibold mb-3 md:mb-4">Quick Links</h3>
            <ul className="space-y-1 md:space-y-2 text-sm md:text-base">
              <li><a href="/tours" className="text-warm-cream/80 hover:text-golden-orange transition-colors">All Tours</a></li>
              <li><a href="/destinations" className="text-warm-cream/80 hover:text-golden-orange transition-colors">Destinations</a></li>
              <li><a href="/about" className="text-warm-cream/80 hover:text-golden-orange transition-colors">About Us</a></li>
              <li><a href="/reviews" className="text-warm-cream/80 hover:text-golden-orange transition-colors">Reviews</a></li>
              <li><a href="/blog" className="text-warm-cream/80 hover:text-golden-orange transition-colors">Travel Blog</a></li>
              <li><a href="/travel-resources" className="text-warm-cream/80 hover:text-golden-orange transition-colors">Travel Resources</a></li>
              <li><a href="/gallery" className="text-warm-cream/80 hover:text-golden-orange transition-colors">Photo Gallery</a></li>
            </ul>
          </div>

          {/* Tour Types */}
          <div>
            <h3 className="text-base md:text-lg font-semibold mb-3 md:mb-4">Safari Types</h3>
            <ul className="space-y-1 md:space-y-2 text-sm md:text-base">
              <li><a href="/tours/luxury" className="text-warm-cream/80 hover:text-golden-orange transition-colors">Luxury Safari</a></li>
              <li><a href="/tours/budget" className="text-warm-cream/80 hover:text-golden-orange transition-colors">Budget Safari</a></li>
              <li><a href="/tours/family" className="text-warm-cream/80 hover:text-golden-orange transition-colors">Family Safari</a></li>
              <li><a href="/tours/photography" className="text-warm-cream/80 hover:text-golden-orange transition-colors">Photography Tours</a></li>
              <li><a href="/tours/cultural" className="text-warm-cream/80 hover:text-golden-orange transition-colors">Cultural Tours</a></li>
              <li><a href="/tours/climbing" className="text-warm-cream/80 hover:text-golden-orange transition-colors">Kilimanjaro Climbing</a></li>
            </ul>
          </div>

          {/* Contact & Newsletter */}
          <div>
            <h3 className="text-base md:text-lg font-semibold mb-3 md:mb-4">Contact Us</h3>
            <div className="space-y-2 md:space-y-3 mb-4 md:mb-6">
              <div className="flex items-center text-warm-cream/80 text-sm">
                <MapPin className="h-3 w-3 md:h-4 md:w-4 mr-2 flex-shrink-0" />
                <span>Arusha, Tanzania</span>
              </div>
              <div className="flex items-center text-warm-cream/80 text-sm">
                <Phone className="h-3 w-3 md:h-4 md:w-4 mr-2 flex-shrink-0" />
                <span>+255 784 123 456</span>
              </div>
              <div className="flex items-center text-warm-cream/80 text-sm">
                <Mail className="h-3 w-3 md:h-4 md:w-4 mr-2 flex-shrink-0" />
                <span><EMAIL></span>
              </div>
            </div>

            {/* Newsletter Signup */}
            <NewsletterSignup variant="footer" />
          </div>
        </div>

        {/* Certifications & Partnerships */}
        <div className="border-t border-medium-brown pt-6 md:pt-8 mb-6 md:mb-8">
          <h4 className="text-base md:text-lg font-semibold mb-3 md:mb-4 text-center">Our Certifications & Partners</h4>
          <div className="flex flex-wrap justify-center items-center gap-4 md:gap-8 opacity-60 text-xs md:text-sm">
            <div>Tanzania Tourism Board</div>
            <div>TATO Member</div>
            <div>ISO Certified</div>
            <div>Carbon Neutral</div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-medium-brown pt-6 md:pt-8 flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <div className="text-warm-cream/60 text-xs md:text-sm text-center md:text-left">
            © 2025 Warrior of Africa Safari Tours. All rights reserved.
          </div>
          <div className="flex flex-wrap justify-center gap-4 md:gap-6 text-xs md:text-sm">
            <a href="/privacy" className="text-warm-cream/60 hover:text-golden-orange transition-colors">
              Privacy Policy
            </a>
            <a href="/terms" className="text-warm-cream/60 hover:text-golden-orange transition-colors">
              Terms of Service
            </a>
            <a href="/cookies" className="text-warm-cream/60 hover:text-golden-orange transition-colors">
              Cookie Policy
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
