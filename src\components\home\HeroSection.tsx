import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import ScrollExpandMedia from '@/components/ui/scroll-expansion-hero';
import '@/utils/testQuickSearch';

const HeroSection = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const slides = [
    {
      image: 'photo-1472396961693-142e6e269027',
      title: 'Experience the Magic of Tanzania Safari',
      subtitle: 'Witness the Great Migration and encounter Africa\'s Big Five in their natural habitat',
    },
    {
      image: 'photo-1466721591366-2d5fba72006d',
      title: 'Luxury Safari Adventures Await',
      subtitle: 'Premium accommodations and expert guides for an unforgettable journey',
    },
    {
      image: 'photo-1493962853295-0fd70327578a',
      title: 'Cultural Immersion & Wildlife',
      subtitle: 'Connect with local communities while exploring pristine wilderness',
    }
  ];

  // Use data from the first slide for ScrollExpandMedia
  const heroSlide = slides[0];
  // Placeholder video URL, replace with your actual video source
  const videoSrc = "https://qconvxhtavuzjdwuowpt.supabase.co/storage/v1/object/public/photoz/Generated%20File%20June%2017,%202025%20-%204_08AM.mp4"; 

  return (
    <ScrollExpandMedia
      mediaType="video"
      mediaSrc={videoSrc}
      posterSrc={`https://qconvxhtavuzjdwuowpt.supabase.co/storage/v1/object/public/photoz/image%20(5).png`}
      bgImageSrc={`https://qconvxhtavuzjdwuowpt.supabase.co/storage/v1/object/public/photoz/image%20(5).png`}
      title={heroSlide.title}
      scrollToExpand={heroSlide.subtitle}
      textBlend
    >
      {/* Clean hero content with just action buttons */}
      <div className="container mx-auto px-4 py-10 text-center">
        <div className="flex flex-col sm:flex-row justify-center gap-4 mt-8">
          <Button
            size="lg"
            onClick={() => navigate('/tours')}
            className="bg-safari-sunset hover:bg-safari-bright-orange text-primary-foreground"
          >
            Explore All Tours
          </Button>
          <Button
            size="lg"
            variant="outline"
            onClick={() => navigate('/tour-builder')}
            className="text-gray-900 border-orange-600 hover:bg-orange-600 hover:text-white"
          >
            Plan Custom Trip
          </Button>
        </div>
      </div>
    </ScrollExpandMedia>
  );
};

export default HeroSection;
